<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NResult, NSpin } from 'naive-ui'
import { getAuthGetTokenOutwardId, getBizAgentDetailId } from '@/api/agent'
import { useAgentUserStore, useChatStore } from '@/store'
import ms from '@/utils/message'
import '@/typings/agent.d.ts'
import { setRequestAgentContext } from '@/utils/token-manager'
import { getOutwardOpenDetail } from '@/api/external-release'
import type { OutwardVo } from '@/api/external-release/type'
import { createGuestOutwardApply } from '@/api'

// 路由和状态
const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()

/**
 * 从路由中获取 对外发布的id
 */
const releaseId = computed(() => route.params.agentId as string)
console.log('route.params.agentId', releaseId.value)

// 获取助手特定的用户Store
const agentUserStore = useAgentUserStore(releaseId.value)
// console.log('agentUserStore', agentUserStore)
console.log('agentUserStore.token', agentUserStore.token)

// 组件状态
const loading = ref(true)
const startingChat = ref(false)
const agentInfo = ref<Agent.AgentVo | null>(null)
const password = ref('') // 密码输入
const passwordError = ref(false) // 密码错误状态

// 对外发布详情
const outwardInfo = ref<OutwardVo | null>(null)
// 是否需要密码
const needAuth = computed(() => {
  return outwardInfo.value?.needAuth || false
})
/**
 * 获取对外发布详情
 */
const fetchIytwardInfo = async () => {
  loading.value = true

  getOutwardOpenDetail(releaseId.value)
    .then(res => {
      if (res.code === 200) {
        outwardInfo.value = res.data
        console.log('对外发布详情:', outwardInfo.value)
      } else {
        ms.error('获取对外发布详情失败')
      }
    })
    .catch(error => {
      console.error('获取对外发布详情失败:', error)
      ms.error('获取对外发布详情失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// // 获取助手信息
// const fetchAgentInfo = async () => {
//   try {
//     loading.value = true
//     // resolved 为什么此处使用getToken而不是操作store获取token
//     // 修复：使用agentUserStore.token而不是getToken()来保持状态管理的一致性
//     if (!agentUserStore.token) {
//       // 第一步：获取临时token
//       const tokenResponse = await getAuthGetTokenOutwardId<{ access_token: string }>({
//         outwardId: agentId.value,
//       })

//       // console.log('tokenResponse', tokenResponse)
//       // console.log('tokenResponse', tokenResponse.data?.access_token)
//       if (!tokenResponse.data?.access_token) {
//         throw new Error('获取访问权限失败')
//       }

//       // 设置token - 通过助手特定的store统一管理
//       agentUserStore.setUserToken(tokenResponse.data?.access_token)
//     }
//     await agentUserStore.getInfo()
//     // 第二步：使用临时token获取助手详情
//     const { data } = await getBizAgentDetailId<Agent.AgentVo>({
//       id: releaseId.value,
//     })
//     agentInfo.value = data
//   } catch (error) {
//     console.error('获取助手信息失败:', error)
//     agentInfo.value = null
//   } finally {
//     loading.value = false
//   }
// }

// 开始试用助手
const handleStartTrial = async () => {
  if (!releaseId.value) {
    ms.error('发布信息无效')
    return
  }

  // 如果需要密码验证
  if (needAuth.value) {
    if (!password.value) {
      passwordError.value = true
      ms.error('请输入访问密码')
      return
    }
    passwordError.value = false
  }

  // if (!agentUserStore.token) {
  // 第一步：获取临时token
  const tokenResponse = await getAuthGetTokenOutwardId<{ access_token: string }>({
    outwardId: releaseId.value,
    password: password.value,
  })

  // console.log('tokenResponse', tokenResponse)
  // console.log('tokenResponse', tokenResponse.data?.access_token)
  if (!tokenResponse.data?.access_token) {
    throw new Error('获取访问权限失败')
  }

  // 设置token - 通过助手特定的store统一管理
  agentUserStore.setUserToken(tokenResponse.data?.access_token)
  // }
  // await agentUserStore.getInfo()

  console.log('临时账号toekn', agentUserStore)

  try {
    startingChat.value = true
    const { data } = await createGuestOutwardApply(releaseId.value)

    if (data?.id) {
      // 添加历史记录
      chatStore.addHistory({
        title: agentInfo.value?.name || '默认对话 New',
        conversationId: data.id,
      })

      // 跳转到聊天页面
      router.push({
        name: 'public-chat',
        params: { conversationId: data.id },
        query: { releaseId: releaseId.value },
      })
      ms.success('已为您创建新的对话会话')
    } else {
      ms.error('创建对话失败，请稍后重试')
    }
  } catch (error) {
    console.error('创建对话失败:', error)
    ms.error('创建对话失败，请稍后重试')
  } finally {
    startingChat.value = false
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 监听releaseId变化，设置全局上下文
watch(
  releaseId,
  newReleaseId => {
    if (newReleaseId) {
      setRequestAgentContext(newReleaseId)
      console.log('Public页面 - releaseId变化:', newReleaseId)
    }
  },
  { immediate: true },
)

// 组件挂载时获取助手信息
onMounted(() => {
  if (releaseId.value) {
    fetchIytwardInfo()
  } else {
    loading.value = false
  }
})
</script>

<template>
  <div class="public-agent-container">
    <!-- 页面加载状态 -->
    <div v-if="loading" class="loading-container">
      <NSpin size="large">
        <template #description>正在加载页面...</template>
      </NSpin>
    </div>

    <!-- 助手信息展示 -->
    <div v-else-if="outwardInfo" class="agent-info-container">
      <!-- 助手头部信息 -->
      <div class="agent-header">
        <div class="agent-avatar" :style="{ backgroundColor: outwardInfo.agentBackgroundColor }">
          <img v-if="outwardInfo.agentEmoji" :src="outwardInfo.agentEmoji" class="emoji" />
          <div v-else class="default-avatar">🤖</div>
        </div>
        <div class="agent-details">
          <h1 class="agent-name">{{ outwardInfo.agentName }}</h1>
          <p class="agent-description">
            {{ outwardInfo.remark || '暂无描述' }}
          </p>
        </div>
      </div>

      <!-- 助手使用信息 -->
      <div class="usage-info">
        <div class="info-item">
          <span class="info-label">创建人:</span>
          <span class="info-value">
            {{ outwardInfo.createByName }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">有效期至:</span>
          <span class="info-value">{{ outwardInfo.deadline || '长期有效' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">模型消耗:</span>
          <span class="info-value">
            {{ outwardInfo.tokens || 0 }}/{{ outwardInfo.maxTokens || '无限制' }} tokens
          </span>
        </div>
      </div>

      <!-- 密码输入 (当需要验证时显示) -->
      <div v-if="needAuth" class="password-section">
        <n-input
          v-model:value="password"
          type="password"
          placeholder="请输入访问密码"
          :status="passwordError ? 'error' : undefined"
          size="large"
          show-password-on="click"
        />
        <div v-if="passwordError" class="password-error">密码错误，请重新输入</div>
      </div>

      <!-- 试用按钮 -->
      <div class="action-section">
        <NButton
          type="primary"
          size="large"
          :loading="startingChat"
          :disabled="needAuth && !password"
          @click="handleStartTrial"
        >
          进入
        </NButton>
        <p class="action-hint">点击进入，将为您创建一个新的对话会话</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <NResult status="404" title="页面不存在" description="您访问的页面不存在或已被删除">
        <template #footer>
          <NButton @click="goHome">返回首页</NButton>
        </template>
      </NResult>
    </div>
  </div>
</template>

<style scoped lang="less">
.public-agent-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.agent-info-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.agent-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
}

.agent-avatar {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .emoji {
    width: 50px;
    height: 50px;
    object-fit: contain;
  }

  .default-avatar {
    font-size: 40px;
  }
}

.agent-details {
  flex: 1;
}

.agent-name {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #333;
}

.agent-description {
  font-size: 16px;
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.agent-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 16px;
  font-size: 14px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  color: #999;
  margin-right: 8px;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

.usage-info {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #666;
  min-width: 80px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.agent-status {
  margin-top: 12px;
}

.prompt-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: #333;
  }

  .prompt-content {
    font-size: 14px;
    color: #555;
    line-height: 1.6;
    white-space: pre-wrap;
  }
}

@media (max-width: 768px) {
  .agent-meta,
  .usage-info {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .info-item,
  .meta-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label,
  .meta-label {
    min-width: auto;
    margin-bottom: 4px;
  }
}

.password-section {
  text-align: center;
  width: 80%;
  min-width: 300px;
  margin: 0 auto 20px;

  .password-error {
    color: #f56c6c;
    font-size: 14px;
    margin-top: 8px;
  }
}

.action-section {
  text-align: center;
  margin-bottom: 30px;

  .n-button {
    width: 200px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }

  .action-hint {
    margin-top: 12px;
    font-size: 14px;
    color: #666;
  }
}

.usage-guide {
  border-top: 1px solid #eee;
  padding-top: 20px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #333;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    color: #666;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .public-agent-container {
    padding: 10px;
  }

  .agent-info-container {
    padding: 20px;
  }

  .agent-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .agent-avatar {
    width: 60px;
    height: 60px;

    .emoji {
      width: 40px;
      height: 40px;
    }

    .default-avatar {
      font-size: 32px;
    }
  }

  .agent-name {
    font-size: 24px;
  }

  .action-section .n-button {
    width: 100%;
  }
}
</style>
