import { ref } from 'vue'
import { usePublicChat } from './usePublicChat'
import { getBizConversationDetail, getBizGuestConversationDetail } from '@/api'
import { useChatStore } from '@/store'

interface ChatHistory extends Chat.History {
  isEditing?: boolean
}

export function useConversationDetail() {
  const conversationDetail = ref<Chat.ConversationVo | null>(null)
  const kbList = ref<KnowledgeBase.KnowledgeBaseVo[]>([])
  const chatStore = useChatStore()
  const { isPublicChat } = usePublicChat()
  const getConversationDetail = isPublicChat
    ? getBizGuestConversationDetail
    : getBizConversationDetail
  // 延迟更新kbList，因为chatStore.history是异步获取的，所以需要延迟更新，不然会被leftsider的覆盖
  async function fetchConversationDetailUpdate(conversationId: string) {
    // let flag = 0
    // const timer = setInterval(async () => {
    //   console.log('flag', flag)
    //   if (chatStore.history?.length) {
    //     clearInterval(timer)
    //     await chatStore.updateHistory(conversationId, { kbList: kbList.value })
    //   } else if (flag > 100) {
    //     clearInterval(timer)
    //     await chatStore.updateHistory(conversationId, { kbList: kbList.value })
    //   }
    //   flag++
    // }, 10)
    setTimeout(() => {
      chatStore.updateHistory(conversationId, { kbList: kbList.value })
    }, 1000)
  }
  // 获取会话详情
  async function fetchConversationDetail(conversationId: string) {
    try {
      const { data } = await getConversationDetail<Chat.ConversationVo>({
        id: conversationId,
      })
      conversationDetail.value = data
      // getConversationDetail比createConversation返回的字段更充实
      // 关于创建对话时候，会话列表抖动的问题
      // 方案1：后端在返回体增加创建时间
      // 方案2：后端在返回体增加创建时间并补全已有字段
      chatStore.updateHistory(conversationId, {
        ...data,
        title: data.name || '默认对话 New',
        summary: data.subtitle,
        conversationId: data.id,
        isEditing: false,
      })
      // 该数据原本以为从会话列表里可以获取，但实际上后端漏了，所以这里需要手动获取
      if (data?.kbList && data.kbList.length) {
        kbList.value = data.kbList
        fetchConversationDetailUpdate(conversationId)
      }
      return data
    } catch (error) {
      console.error('获取对话详情失败:', error)
      return null
    }
  }

  return {
    conversationDetail,
    kbList,
    fetchConversationDetail,
  }
}
