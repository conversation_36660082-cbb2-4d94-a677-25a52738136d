.markdown-body {
  background-color: transparent;
  font-size: 16px;
  line-height: 28px;
  color: #30343a;
  font-family:
    "Alibaba PuHuiTi",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    <PERSON><PERSON>,
    "Helvetica Neue",
    <PERSON><PERSON>,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";

  p {
    white-space: pre-wrap;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }

  pre code,
  pre tt {
    line-height: 1.65;
  }

  .highlight pre,
  pre {
    background-color: #fff;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: #65a665;
        }
      }
    }
  }

  // 思维链样式
  :deep(.thinking-chain) {
    position: relative;
    background-color: rgba(76, 92, 236, 0.05);
    border: 1px solid rgba(76, 92, 236, 0.1);
    border-left: 4px solid #4c5cec;
    padding: 1rem 1rem 1rem 2.5rem;
    margin: 1rem 0;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    line-height: 1.6;

    // &::before {
    // 	content: "思维过程";
    // 	position: absolute;
    // 	top: 0.5rem;
    // 	left: 1rem;
    // 	font-weight: bold;
    // 	color: #4C5CEC;
    // 	font-size: 0.85em;
    // }

    &::after {
      content: "";
      position: absolute;
      top: 1rem;
      left: 1rem;
      width: 1px;
      height: calc(100% - 2rem);
      background: rgba(76, 92, 236, 0.2);
    }
  }

  // Mermaid
  div[id^="mermaid-container"] {
    padding: 4px;
    border-radius: 4px;
    overflow-x: auto !important;
    background-color: #fff;
    border: 1px solid #e5e5e5;
  }

  &.markdown-body-generate > dd:last-child:after,
  &.markdown-body-generate > dl:last-child:after,
  &.markdown-body-generate > dt:last-child:after,
  &.markdown-body-generate > h1:last-child:after,
  &.markdown-body-generate > h2:last-child:after,
  &.markdown-body-generate > h3:last-child:after,
  &.markdown-body-generate > h4:last-child:after,
  &.markdown-body-generate > h5:last-child:after,
  &.markdown-body-generate > h6:last-child:after,
  &.markdown-body-generate > li:last-child:after,
  &.markdown-body-generate > ol:last-child li:last-child:after,
  &.markdown-body-generate > p:last-child:after,
  &.markdown-body-generate > pre:last-child code:after,
  &.markdown-body-generate > td:last-child:after,
  &.markdown-body-generate > ul:last-child li:last-child:after {
    animation: blink 1s steps(5, start) infinite;
    color: #000;
    content: "_";
    font-weight: 700;
    margin-left: 3px;
    vertical-align: baseline;
  }

  @keyframes blink {
    to {
      visibility: hidden;
    }
  }
}

html.dark {
  .markdown-body {
    // 暗色模式下的思维链样式
    .thinking-chain {
      background-color: rgba(76, 92, 236, 0.05);
      border-color: rgba(76, 92, 236, 0.2);
      border-left-color: #4c5cec;
      color: #bbb;

      &::before {
        color: #4c5cec;
      }

      &::after {
        background: rgba(76, 92, 236, 0.2);
      }
    }

    &.markdown-body-generate > dd:last-child:after,
    &.markdown-body-generate > dl:last-child:after,
    &.markdown-body-generate > dt:last-child:after,
    &.markdown-body-generate > h1:last-child:after,
    &.markdown-body-generate > h2:last-child:after,
    &.markdown-body-generate > h3:last-child:after,
    &.markdown-body-generate > h4:last-child:after,
    &.markdown-body-generate > h5:last-child:after,
    &.markdown-body-generate > h6:last-child:after,
    &.markdown-body-generate > li:last-child:after,
    &.markdown-body-generate > ol:last-child li:last-child:after,
    &.markdown-body-generate > p:last-child:after,
    &.markdown-body-generate > pre:last-child code:after,
    &.markdown-body-generate > td:last-child:after,
    &.markdown-body-generate > ul:last-child li:last-child:after {
      color: #65a665;
    }
  }

  .message-reply {
    .whitespace-pre-wrap {
      white-space: pre-wrap;
      color: var(--n-text-color);
    }
  }

  .highlight pre,
  pre {
    background-color: #282c34;
  }
}

@media screen and (max-width: 533px) {
  .markdown-body .code-block-wrapper {
    padding: unset;

    code {
      padding: 24px 16px 16px 16px;
    }
  }
}
