<script lang="ts" setup>
// Vue 核心
import { computed, onMounted, onUnmounted, onUpdated, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'

// 第三方库
import MarkdownIt from 'markdown-it'
import MdKatex from '@vscode/markdown-it-katex'
import MdLinkAttributes from 'markdown-it-link-attributes'
import MdMermaid from 'mermaid-it-markdown'
import MdFootnote from 'markdown-it-footnote'
import hljs from 'highlight.js'
import { NCollapse, NCollapseItem } from 'naive-ui'
import { ElDialog, ElDrawer, ElProgress } from 'element-plus'
import { prepareMarkdownText } from './markdown-log'

// 本地导入

// 项目内部导入
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { copyToClip } from '@/utils/copy'
import { useSettingStore } from '@/store'
import KkviewPreview from '@/views/knowledge-base/common/file-preview/components/KkviewPreview.vue'

interface Props {
  inversion?: boolean
  error?: boolean
  text?: string
  loading?: boolean
  asRawText?: boolean
  toolExecutionList?: Chat.ToolExecution[]
  chatContentRecordList?: Chat.ChatContentRecord[]
  reasoningContent?: string
}

const props = defineProps<Props>()

const { isMobile } = useBasicLayout()

// Refs
const textRef = ref<HTMLElement>()
const displayedText = ref('')
const fullText = ref('')
const isTyping = ref(false)
const showPreviewModal = ref(false)
const currentDialogContent = ref('')
const copyIconStatus = ref<{ [key: number]: boolean }>({})
const thinkingCopyStatus = ref<{ [key: number]: boolean }>({})
const showFilePreview = ref(false)
const currentPreviewFileUrl = ref('')
const currentFileName = ref('文件预览')

// 添加推理内容展开状态管理
const reasoningCollapseValue = ref<string[]>([])
const hasAutoCollapsed = ref(false) // 标记是否已经自动闭合过

// Store and Layout
const settingStore = useSettingStore()
const { typewriterEffect } = storeToRefs(settingStore)

// Typing effect variables
const typingSpeed = 15
const typingBatchSize = 3
let typingTimer: number | null = null

// Markdown-it instances and plugins
const mdBaseConfig = {
  html: false,
  linkify: true,
}

const mdi = new MarkdownIt({
  ...mdBaseConfig,
  highlight(code, language) {
    if (language === 'thinking-chain') return `<pre class="thinking-chain">${code}</pre>`

    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

const thinkingMdi = new MarkdownIt({
  ...mdBaseConfig,
  highlight: code => code,
})
  .disable('code')
  .disable('fence')

// resolved 思维链提取逻辑已移至 streamResponseManager.ts

const mdPlugins = [
  { plugin: MdLinkAttributes, options: { attrs: { target: '_blank', rel: 'noopener' } } },
  { plugin: MdKatex, options: {} },
  { plugin: MdMermaid, options: {} },
  { plugin: MdFootnote, options: {} },
]

mdPlugins.forEach(({ plugin, options }) => {
  thinkingMdi.use(plugin, options)
  mdi.use(plugin, options)
})

/**
 * 渲染 Markdown 文本为 HTML
 * 使用 markdown-it 渲染引擎，配合数学公式与语法高亮等插件
 *
 * @param {string} text - 预处理后的 Markdown 文本
 * @returns {string} - 渲染后的 HTML 内容
 */
const renderMarkdown = (text: string) => {
  const preparedText = prepareMarkdownText(text)
  return mdi.render(preparedText)
}

/**
 * 处理文本内容并转换为 HTML
 * 该方法执行多个步骤：
 * 1. 如果是原始文本，直接返回
 * 2. 提取思维链内容
 * 3. 移除思维链标记，保证不在最终输出中显示
 * 4. 通过 renderMarkdown 渲染为 HTML
 *
 * @param {string} value - 原始文本内容
 * @param {boolean} asRawText - 是否作为原始文本处理（不解析 Markdown）
 * @returns {string} - 处理后的 HTML 或原始文本
 */
const processTextToHtml = (value: string, asRawText: boolean) => {
  if (asRawText) return value
  // resolved 思维链处理逻辑已移至 streamResponseManager.ts，这里直接渲染
  return renderMarkdown(value)
}

const html = computed(() => {
  const value = props.text ?? ''
  if (props.inversion || props.asRawText) {
    return value
  }
  const textToRender =
    !typewriterEffect.value || !isTyping.value || props.loading === false
      ? value
      : displayedText.value
  return processTextToHtml(textToRender, false)
})

/**
 * 生成代码块的 HTML，包含语言标识和复制按钮
 *
 * @param {string} str - 高亮处理后的代码内容
 * @param {string} [lang] - 代码语言标识
 * @returns {string} - 包含复制功能的代码块 HTML
 */
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy cursor-pointer">${t('chat.copyCode')}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

// Computed properties
const wrapClass = computed(() => {
  return [
    'text-wrap',
    'min-w-[20px]',
    'rounded-md',
    isMobile.value ? 'p-2' : 'px-3 py-3',
    props.inversion ? 'bg-[#4C5CEC] text-white' : 'bg-[#fff]',
    props.inversion ? 'dark:bg-[#a1dc95]' : 'dark:bg-[#1e1e20]',
    props.inversion ? 'message-request' : 'message-reply',
    { 'text-red-500': props.error },
  ]
})

/**
 * 实现打字机效果
 * 从起始文本逐步过渡到目标文本，可以模拟实时输入效果
 *
 * @param {string} startText - 起始文本内容
 * @param {string} targetText - 目标文本内容（最终显示）
 */
function startTypewriterEffect(startText: string, targetText: string) {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  isTyping.value = true
  displayedText.value = startText
  if (displayedText.value === targetText) {
    isTyping.value = false
    return
  }
  let currentIndex = displayedText.value.length
  const typeNextChar = () => {
    if (currentIndex < targetText.length) {
      const endIndex = Math.min(currentIndex + typingBatchSize, targetText.length)
      displayedText.value = targetText.substring(0, endIndex)
      currentIndex = endIndex
      const hasSpecialChars = /[`<|]/.test(
        targetText.substring(Math.max(0, currentIndex - 20), currentIndex),
      )
      typingTimer = window.setTimeout(typeNextChar, hasSpecialChars ? typingSpeed / 2 : typingSpeed)
    } else {
      isTyping.value = false
    }
  }
  typingTimer = window.setTimeout(typeNextChar, typingSpeed)
}

/**
 * 清理资源，主要是清除打字机效果的定时器
 */
function cleanup() {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
}

/**
 * 为代码块的复制按钮添加事件监听
 * 使用户可以通过点击复制按钮复制代码内容
 */
function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach(btn => {
      btn.addEventListener('click', () => {
        const code = btn.parentElement?.nextElementSibling?.textContent
        if (code) {
          copyToClip(code).then(() => {
            btn.textContent = t('chat.copied')
            setTimeout(() => {
              btn.textContent = t('chat.copyCode')
            }, 1000)
          })
        }
      })
    })
  }
}

/**
 * 格式化工具执行结果为 JSON 字符串
 * 处理 arguments 和 result，并格式化输出
 *
 * @param {Chat.ToolExecution} tool - 工具执行结果对象
 * @returns {string} - 格式化后的 JSON 字符串
 */
const formatToolContent = (tool: Chat.ToolExecution) => {
  let args = {}
  let result = {}
  try {
    args = tool.arguments ? JSON.parse(tool.arguments) : {}
  } catch (e) {
    args = { error: 'Failed to parse arguments' }
  }
  try {
    result = tool.result ? JSON.parse(tool.result) : {}
  } catch (e) {
    result = { error: 'Failed to parse result' }
  }
  return JSON.stringify({ arguments: args, result }, null, 2)
}

/**
 * 复制工具执行结果内容
 * 复制成功后会更新复制按钮状态
 *
 * @param {Chat.ToolExecution} tool - 工具执行结果对象
 * @param {number} index - 工具索引
 */
const copyToolContent = (tool: Chat.ToolExecution, index: number) => {
  copyToClip(formatToolContent(tool)).then(() => {
    copyIconStatus.value = { ...copyIconStatus.value, [index]: true }
    setTimeout(() => {
      copyIconStatus.value = { ...copyIconStatus.value, [index]: false }
    }, 1000)
  })
}

/**
 * 复制思维链内容
 * 复制成功后会更新复制按钮状态
 *
 * @param {string} content - 思维链内容
 * @param {number} index - 思维链索引
 */
const copyThinkingContent = (content: string, index: number) => {
  copyToClip(content).then(() => {
    thinkingCopyStatus.value = { ...thinkingCopyStatus.value, [index]: true }
    setTimeout(() => {
      thinkingCopyStatus.value = { ...thinkingCopyStatus.value, [index]: false }
    }, 1000)
  })
}

/**
 * 预览工具执行结果内容
 * 在弹窗中以格式化方式显示完整内容
 *
 * @param {Chat.ToolExecution} tool - 工具执行结果对象
 */
const previewToolContent = (tool: Chat.ToolExecution) => {
  currentDialogContent.value = formatToolContent(tool)
  showPreviewModal.value = true
}

/**
 * 预览思维链内容
 * 在弹窗中显示完整思维链内容
 *
 * @param {string} content - 思维链内容
 */
const previewThinkingContent = (content: string) => {
  currentDialogContent.value = content
  showPreviewModal.value = true
}

/**
 * 打开文件预览抽屉
 * 使用 KkviewPreview 组件预览文件
 *
 * @param {string} fileUrl - 文件 URL
 * @param {string} [fileName] - 文件名，未提供时从 URL 中提取
 */
const openFilePreview = (fileUrl: string, fileName?: string) => {
  currentPreviewFileUrl.value = fileUrl
  if (fileName) {
    currentFileName.value = fileName
  } else {
    try {
      const url = new URL(fileUrl)
      const pathParts = url.pathname.split('/')
      const extractedFileName = pathParts[pathParts.length - 1]
      currentFileName.value = decodeURIComponent(extractedFileName) || '文件预览'
    } catch (error) {
      currentFileName.value = '文件预览'
    }
  }
  showFilePreview.value = true
}

// sse过程中虽然数据不完整，但是每次props.text变化都会触发对原有累加文本的处理，是吗
// 是的，在SSE流式响应过程中，虽然每次接收到的数据可能是不完整的，但每当props.text发生变化时，都会触发对已累加文本的整体重新处理。
watch(
  () => props.text,
  newText => {
    // console.log('watch-newText', newText)
    const newTextValue = newText || ''
    if (newTextValue && !props.inversion && typewriterEffect.value) {
      startTypewriterEffect(
        newTextValue.startsWith(fullText.value) ? fullText.value : '',
        newTextValue,
      )
      fullText.value = newTextValue
    } else {
      displayedText.value = newTextValue
      fullText.value = newTextValue
      isTyping.value = false
    }
  },
  { immediate: true },
)

watch(
  () => props.loading,
  newLoading => {
    if (newLoading === false && fullText.value) {
      displayedText.value = fullText.value
      isTyping.value = false
      if (typingTimer) {
        clearTimeout(typingTimer)
        typingTimer = null
      }
    }
  },
  { immediate: true },
)

// 监听推理内容变化，实现展开/折叠逻辑
watch(
  () => props.reasoningContent,
  (newContent, oldContent) => {
    // 只有在加载状态（实时推理）时才展开，历史记录不展开
    if (newContent && !oldContent && props.loading) {
      reasoningCollapseValue.value = ['reasoning-content']
      hasAutoCollapsed.value = false // 重置自动闭合标记
    }
  },
  { immediate: true },
)

// 监听正文内容变化，当开始接收正文时自动闭合推理区域
watch(
  () => props.text,
  (newText, oldText) => {
    // console.log('watch-newText', newText)
    // console.log('watch-oldText', oldText)

    // 有正文内容且还没有自动闭合过，则自动闭合
    if (newText && !hasAutoCollapsed.value && props.reasoningContent) {
      reasoningCollapseValue.value = []
      hasAutoCollapsed.value = true
    }
  },
)

// Lifecycle hooks
onMounted(() => {
  addCopyEvents()
})

onUpdated(() => {
  addCopyEvents()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <!-- resolved 如果!inversion，class增加w-full -->
  <div
    class="text-black"
    :class="[
      wrapClass,
      {
        'w-full':
          !inversion &&
          ((toolExecutionList && toolExecutionList.length) ||
            (reasoningContent && reasoningContent.length)),
      },
    ]"
  >
    <div ref="textRef" class="leading-relaxed break-words">
      <!-- !inversion -->
      <div v-if="!inversion">
        <!-- 显示思维链内容 -->
        <div
          v-if="reasoningContent && reasoningContent.length > 0"
          class="tool-execution-container mt-2"
        >
          <NCollapse v-model:expanded-names="reasoningCollapseValue">
            <NCollapseItem key="reasoning-content" name="reasoning-content">
              <template #header>
                <div class="tool-header flex items-center w-full">
                  <div class="flex-1">
                    <span class="tool-name">已深度思考</span>
                    <!-- <span style="" class="border-l ml-2 pl-2">用时 xxx 秒</span> -->
                  </div>
                  <div class="ml-auto flex items-center">
                    <div class="w-[20px] h-[20px] flex justify-center items-center">
                      <i
                        :style="thinkingCopyStatus[0] ? '' : `font-size: 1.5em; margin-top: -2px;`"
                        class="iconfont cursor-pointer hover:text-[#4c5cec]"
                        :class="thinkingCopyStatus[0] ? 'icon-yansexuanze' : 'icon-fuzhi'"
                        @click.stop="copyThinkingContent(reasoningContent, 0)"
                      ></i>
                    </div>
                  </div>
                </div>
              </template>

              <div class="top-content">
                <div
                  class="whitespace-pre-wrap bg-white dark:bg-gray-700 p-2 rounded overflow-auto"
                >
                  <div v-html="thinkingMdi.render(reasoningContent)"></div>
                </div>
              </div>
            </NCollapseItem>
          </NCollapse>
        </div>
        <!-- 显示工具执行结果 -->
        <div
          v-if="toolExecutionList && toolExecutionList.length"
          class="tool-execution-container mt-2"
        >
          <NCollapse>
            <NCollapseItem
              v-for="(tool, index) in toolExecutionList"
              :key="index"
              :name="index.toString()"
            >
              <!-- resolved 鼠标点击头部折叠时候，有时候会出现把下面内容全部选中的蓝色选中效果，为什么 -->
              <template #header>
                <div class="tool-header flex items-center w-full">
                  <div class="flex-1">
                    <span class="tool-name">{{ tool.name }}</span>
                    <span style="color: #4c5cec" class="border-l ml-2 pl-2"
                      >已完成 <i class="iconfont icon-yansexuanze"></i
                    ></span>
                  </div>
                  <!-- resolved 最右边放一个能够展开弹窗去预览的按钮（icon）和复制按钮（icon） -->
                  <div class="ml-auto flex items-center">
                    <div class="w-[20px] h-[20px] flex justify-center items-center">
                      <i
                        class="iconfont icon-yulan mr-2 cursor-pointer hover:text-[#4c5cec]"
                        @click.stop="previewToolContent(tool)"
                      />
                    </div>
                    <!-- resolved 复制成功后icon-fuzhi改为icon-yansexuanze -->
                    <div class="w-[20px] h-[20px] flex justify-center items-center">
                      <i
                        :style="copyIconStatus[index] ? '' : `font-size: 1.5em; margin-top: -2px;`"
                        class="iconfont cursor-pointer hover:text-[#4c5cec]"
                        :class="copyIconStatus[index] ? 'icon-yansexuanze' : 'icon-fuzhi'"
                        @click.stop="copyToolContent(tool, index)"
                      ></i>
                    </div>
                  </div>
                </div>
              </template>

              <!-- resolved 这些内容组装成一个完整json模样 -->
              <div class="top-content">
                <pre
                  class="whitespace-pre-wrap bg-white dark:bg-gray-700 p-2 rounded overflow-auto"
                  >{{ formatToolContent(tool) }}</pre
                >
              </div>
            </NCollapseItem>
          </NCollapse>
        </div>
        <!-- 预览弹窗 -->
        <ElDialog
          v-model="showPreviewModal"
          title="内容预览"
          style="width: 80vw"
          :append-to-body="false"
        >
          <pre
            class="whitespace-pre-wrap bg-white dark:bg-gray-700 p-2 rounded overflow-auto"
            style="max-height: calc(80vh - 120px)"
            >{{ currentDialogContent }}</pre
          >
        </ElDialog>
        <!-- 文件预览抽屉 -->
        <!-- resolved title改为对应文件名 metadata.fileName -->
        <ElDrawer
          v-model="showFilePreview"
          :title="currentFileName"
          :size="isMobile ? '100%' : '70%'"
          :with-header="true"
          :destroy-on-close="false"
        >
          <KkviewPreview
            v-if="currentPreviewFileUrl"
            :file-url="currentPreviewFileUrl"
            :default-pdf-mode="true"
          />
        </ElDrawer>
        <!-- =====主要内容/正文===== -->
        <div
          v-if="!asRawText"
          class="markdown-body"
          :class="{ 'markdown-body-generate': loading }"
          v-html="html"
        />
        <div v-else class="whitespace-pre-wrap text-base" v-text="html" />
      </div>
      <!-- inversion/用户发出消息 -->
      <div v-else class="whitespace-pre-wrap text-base" v-text="html" />
      <!-- 引用片段 -->
      <!-- resolved 从父组件传入chatContentRecordList  -->
      <div v-if="chatContentRecordList && chatContentRecordList.length" class="container-footer">
        <!-- resolved 鼠标hover时候展示el-popover，内容是chatContentRecordList -->
        <el-popover
          trigger="hover"
          placement="top-start"
          :width="360"
          :offset="6"
          popper-class="record-popover-popper"
        >
          <template #reference>
            <div class="record-btn">
              <i class="iconfont icon-zhishiku iconfont-c"></i>
              <span>
                知识库引用<span style="font-weight: bold" class="px-1">{{
                  chatContentRecordList.length
                }}</span
                >个来源参考
              </span>
            </div>
          </template>
          <div class="knowledge-record-popover">
            <div class="record-list">
              <div
                v-for="(record, index) in chatContentRecordList"
                :key="index"
                class="record-item"
              >
                <!-- resolved 已更新src\typings\chat.d.ts的RAGMetadataDto，再处理一下 -->
                <div class="record-item-header flex items-center justify-between">
                  <div class="record-item-header-index">
                    <span style="color: #b6bac1">#</span>
                    <span style="color: #646a73" class="ml-1">{{ index + 1 }}</span>
                  </div>
                  <div v-if="record.metadata?.fileUrl" class="record-item-footer">
                    <!-- resolved 查看文件的时候弹出eldrawer，内容是文件预览，预览方式参考src\views\knowledge-base\common\file-preview\components\KkviewPreview.vue -->
                    <span
                      class="file-link"
                      style="text-decoration: none; color: #4865e8"
                      @click.prevent="
                        openFilePreview(record.metadata.fileUrl, record.metadata.fileName)
                      "
                      >查看文件 <i class="iconfont icon-tiaozhuan mr-2 cursor-pointer"
                    /></span>
                  </div>
                </div>
                <div class="record-item-content mt-2">{{ record.textSegment }}</div>
                <div class="record-item-footer flex items-center justify-between">
                  <!-- 分片字数 -->
                  <span class="record-score">
                    <i class="iconfont icon-zishu mr-1 cursor-pointer" />
                    {{ record.wordCount }}</span
                  >
                  <!-- 召回次数（命中次数） -->
                  <span class="record-score ml-4">
                    <i class="iconfont icon-zhaohui mr-1 cursor-pointer" />
                    {{ record.recallCount || 0 }}</span
                  >
                  <!-- resolved 使用elementplus的进度条组件，灰色 -->
                  <div class="record-score flex-1 ml-4 flex items-center justify-end">
                    <div class="w-[140px]">
                      <ElProgress
                        :percentage="Number(((record.score || 0) * 100).toFixed(0))"
                        :stroke-width="3"
                        :show-text="true"
                        :format="() => (record.score || 0).toFixed(2)"
                        color="#969BA4"
                        class="flex-1 small-text-progress"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url(@/styles/variables.less);
@import url(./style.less);

.typewriter-cursor {
  display: inline-block;
  width: 8px;
  height: 16px;
  background-color: currentColor;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.tool-execution-container {
  padding-bottom: 8px;
  margin-bottom: 8px;
  border: 1px solid rgb(239, 239, 245); // rgb(239, 239, 245) var(--n-divider-color)
  border-radius: 8px;
  padding: 10px;
}

.top-content {
  max-height: 300px;
  overflow-y: auto;
  // border: 1px solid #e5e7eb;
  // border-radius: 8px;
  padding: 10px;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// 添加防止文本选中的样式
.tool-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

:deep(.n-collapse-item-arrow) {
  display: none !important;
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    margin-right: 0;
    padding: 16px;
    border-bottom: 1px solid @bdc;
  }

  .el-dialog__body {
    padding: 16px;
  }
}

.container-footer {
  border-top: 1px solid #dadde8;
  padding-top: 8px;
  margin-top: 16px;
  display: flex;
  .record-btn {
    display: flex;
    cursor: pointer;
    display: block;
    padding: 0 10px;
    height: 32px;
    line-height: 32px;
    background: rgba(230, 234, 244, 0.6);
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #646a73;
    text-align: center;
    font-style: normal;
  }
}

.knowledge-record-popover {
  border-radius: 8px;
  background: #fff;
  padding: 8px 0;

  .record-list {
    padding: 0 16px;
    max-height: 440px;
    overflow-y: auto;
    .record-item {
      padding: 12px 0;
      margin-bottom: 8px;
      border-radius: 6px;

      &:last-child {
        margin-bottom: 0;
      }
    }
    .record-item + .record-item {
      border-top: 1px solid rgba(218, 221, 232, 0.5);
      // padding-top: 12px;
      // margin-top: 12px;
    }
  }
  .file-link {
    font-size: 12px;
    color: #409eff;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
  .record-item-header {
    .record-item-header-index {
      width: 64px;
      height: 32px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #dadde8;
      text-align: center;
      line-height: 32px;
    }
  }
  .record-item-content {
    font-size: 13px;
    color: #606266;
    line-height: 1.5;
    max-height: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    margin-bottom: 4px;
  }

  .record-item-footer {
    .record-score {
      font-size: 12px;
      color: #909399;
    }
  }
}
.dark {
  .knowledge-record-popover {
    background: #1f1f1f;

    .record-item {
      background: #2d2d2d;

      .record-score {
        color: #a3a6ad;
      }

      .record-item-content {
        color: #cfd3dc;
      }

      .record-item-footer {
      }
    }
  }
}

/* 进度条小字体样式 */
:deep(.small-text-progress) {
  .el-progress__text {
    font-size: 12px !important;
    color: #969ba4 !important;
    min-width: 30px;
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>

<style>
.record-popover-popper {
  padding: 0 !important;
}
</style>
